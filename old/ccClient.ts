/**
 * ClinicalCare (CC) API Client
 *
 * Comprehensive API client for ClinicalCare system providing all CRUD operations
 * for patients, appointments, custom fields, users, services, resources, locations,
 * invoices, and payments.
 *
 * Features:
 * - Complete feature parity with v3Integration
 * - TypeScript typing and error handling
 * - Configuration from configs.ts
 * - Data cleaning utilities
 */

import getConfig from "@config";
import type {
	GetCCAppointmentCategoryType,
	GetCCAppointmentType,
	GetCCCustomField,
	GetCCLocationType,
	GetCCPatientCustomField,
	GetCCPatientType,
	GetCCResourceType,
	GetCCServiceType,
	GetCCUserType,
	GetInvoiceType,
	GetPaymentType,
	PostCCAppointmentType,
	PostCCCustomField,
	PostCCPatientType,
	PutCCAppointmentType,
} from "@type/CCTypes";
import { apiResponseCache } from "@utils/advancedCache";
import cleanData from "@utils/cleanData";
import { logDebug } from "@/utils/logger";

/**
 * Enhanced error interface for API errors with detailed information
 */
interface EnhancedError extends Error {
	details: {
		status: number;
		statusText: string;
		url: string;
		method: string;
		requestData?: unknown;
		responseData?: unknown;
		timestamp: string;
	};
}

/**
 * Validation error structure from API responses
 */
interface ValidationError {
	message?: string;
	detail?: string;
	[key: string]: unknown;
}

/**
 * API error response structure
 */
interface ApiErrorResponse {
	message?: string;
	error?: string;
	errors?: (string | ValidationError)[];
	detail?: string;
	title?: string;
	[key: string]: unknown;
}

/**
 * HTTP request options interface
 */
interface RequestOptions {
	url: string;
	method: "GET" | "POST" | "PUT" | "DELETE";
	data?: unknown;
	params?: Record<string, string | number | boolean>;
	invalidateCache?: boolean;
}

/**
 * Base HTTP client for ClinicalCare API with intelligent caching
 */
const ccRequest = async <
	T extends Record<string, unknown> = Record<string, unknown>,
>(
	options: RequestOptions,
): Promise<T> => {
	const { url, method, data, params, invalidateCache = false } = options;

	// Build full URL with base domain
	const baseUrl = getConfig("ccApiDomain");
	let fullUrl = `${baseUrl}${url}`;

	// Add query parameters if provided
	if (params) {
		const searchParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			searchParams.append(key, String(value));
		});
		fullUrl += `?${searchParams.toString()}`;
	}

	// Generate cache key for GET requests
	const cacheKey = apiResponseCache.generateKey(method, url, params);

	// Check cache for GET requests (unless cache invalidation is requested)
	if (method === "GET" && !invalidateCache) {
		const cachedData = apiResponseCache.get(cacheKey);
		if (cachedData) {
			return cachedData as T;
		}
	}

	// Prepare request headers
	const headers: Record<string, string> = {
		Authorization: `Bearer ${getConfig("ccApiKey")}`,
		Accept: "application/json",
		"Content-Type": "application/json",
	};

	try {
		const response = await fetch(fullUrl, {
			method,
			headers,
			body: data ? JSON.stringify(data) : undefined,
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.log("CC API Error errorText:", errorText);
			let errorMessage: string;
			let errorDetails: Record<string, unknown> = {};

			try {
				const errorData: ApiErrorResponse = JSON.parse(errorText);
				errorDetails = errorData;

				// Extract meaningful error message from various possible structures
				const firstError = errorData.errors?.[0];
				const firstErrorMessage =
					typeof firstError === "string" ? firstError : firstError?.message;

				errorMessage =
					errorData.message ||
					errorData.error ||
					firstErrorMessage ||
					errorData.detail ||
					errorData.title ||
					`HTTP ${response.status}: ${response.statusText}`;

				// If errorData has validation errors, include them
				if (errorData.errors && Array.isArray(errorData.errors)) {
					const validationErrors = errorData.errors
						.map((err: string | ValidationError) =>
							typeof err === "string"
								? err
								: err.message || err.detail || JSON.stringify(err),
						)
						.join("; ");
					errorMessage += ` | Validation errors: ${validationErrors}`;
				}
			} catch {
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
				errorDetails = { rawResponse: errorText };
			}

			// Enhanced error with detailed information
			const enhancedError = new Error(
				`CC API Error: ${errorMessage}`,
			) as EnhancedError;
			enhancedError.details = {
				status: response.status,
				statusText: response.statusText,
				url: fullUrl,
				method,
				requestData: data,
				responseData: errorDetails,
				timestamp: new Date().toISOString(),
			};

			throw enhancedError;
		}

		const responseData = await response.json();

		// Cache successful GET responses
		if (method === "GET") {
			apiResponseCache.set(cacheKey, responseData);
		}

		// Invalidate related cache entries for mutating operations
		if (method === "POST" || method === "PUT" || method === "DELETE") {
			const invalidatedCount = apiResponseCache.invalidatePattern(url);
			if (invalidatedCount > 0) {
				logDebug(
					`CC API: Invalidated ${invalidatedCount} cache entries for ${method} ${url}`,
				);
			}
		}

		return responseData as T;
	} catch (error) {
		if (error instanceof Error) {
			// If this is our enhanced error with details, preserve them
			const enhancedError = error as EnhancedError;
			if (enhancedError.details) {
				const newEnhancedError = new Error(
					`CC API Request Failed: ${error.message}`,
				) as EnhancedError;
				newEnhancedError.details = enhancedError.details;
				throw newEnhancedError;
			}
			throw new Error(`CC API Request Failed: ${error.message}`);
		}
		throw new Error("CC API Request Failed: Unknown error");
	}
};

/**
 * Convert array of IDs to query string format
 */
const idsToQueryString = (ids: number[]): string => {
	return ids
		.map((id) => `ids[]=${id.toString().trim()}`)
		.join("&")
		.trim();
};

/**
 * Patient operations
 */
export const patientReq = {
	/**
	 * Create a new patient
	 */
	create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: "/patients",
			method: "POST",
			data: {
				patient: cleanData(data),
			},
		});
		return response.patient;
	},

	/**
	 * Update an existing patient
	 */
	update: async (
		id: number,
		data: PostCCPatientType,
	): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: `/patients/${id}`,
			method: "PUT",
			data: {
				patient: cleanData(data),
			},
		});
		return response.patient;
	},

	/**
	 * Get a patient by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: `/patients/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.patient;
	},

	/**
	 * Search for a patient by email or phone
	 */
	search: async (
		emailOrPhone: string,
		invalidateCache?: boolean,
	): Promise<GetCCPatientType | null> => {
		const response = await ccRequest<{ patients: GetCCPatientType[] }>({
			url: `/patients`,
			method: "GET",
			params: { search: emailOrPhone },
			invalidateCache,
		});
		return response.patients && response.patients.length > 0
			? response.patients[0]
			: null;
	},

	/**
	 * Get all patients with pagination and filtering
	 */
	all: async (
		params: {
			page?: number;
			perPage?: number;
			active?: boolean;
			sort?: string;
			invalidateCache?: boolean;
		} = {
			page: 1,
			perPage: 20,
			active: true,
			sort: "-createdAt",
		},
	): Promise<GetCCPatientType[]> => {
		const { invalidateCache, ...queryParams } = params;
		const requestParams = {
			[`active${queryParams.active}`]: "",
			"page[number]": queryParams.page || 1,
			"page[size]": queryParams.perPage || 20,
			sort: queryParams.sort || "-createdAt",
		};

		const response = await ccRequest<{ patients: GetCCPatientType[] }>({
			url: "/patients",
			method: "GET",
			params: requestParams,
			invalidateCache,
		});
		return response.patients;
	},

	/**
	 * Get patient custom fields by IDs
	 */
	customFields: async (
		ids: number[],
		invalidateCache?: boolean,
	): Promise<GetCCPatientCustomField[]> => {
		const response = await ccRequest<{
			patientCustomFields: GetCCPatientCustomField[];
		}>({
			url: `/patientCustomFields?${idsToQueryString(ids)}`,
			method: "GET",
			invalidateCache,
		});
		return response.patientCustomFields;
	},
};

/**
 * Custom field operations
 */
export const ccCustomfieldReq = {
	/**
	 * Get a custom field by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCCustomField> => {
		const response = await ccRequest<{ customField: GetCCCustomField }>({
			url: `/customFields/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.customField;
	},

	/**
	 * Get all custom fields
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCCustomField[]> => {
		const response = await ccRequest<{ customFields: GetCCCustomField[] }>({
			url: "/customFields/",
			method: "GET",
			invalidateCache,
		});
		return response.customFields;
	},

	/**
	 * Create a new custom field
	 * Enhanced with production-ready validation and error handling.
	 */
	create: async (data: PostCCCustomField): Promise<GetCCCustomField> => {
		// Input validation
		if (!data) {
			throw new Error("CC Custom Field creation data is required");
		}

		if (!data.name || data.name.trim().length === 0) {
			throw new Error("CC Custom Field name is required and cannot be empty");
		}

		if (!data.label || data.label.trim().length === 0) {
			throw new Error("CC Custom Field label is required and cannot be empty");
		}

		if (!data.type || data.type.trim().length === 0) {
			throw new Error("CC Custom Field type is required and cannot be empty");
		}

		// Validate field name doesn't contain invalid characters
		const invalidChars = /[<>:"/\\|?*]/;
		if (invalidChars.test(data.name)) {
			throw new Error(
				`CC Custom Field name "${data.name}" contains invalid characters`,
			);
		}

		// Validate field type is supported
		const supportedTypes = [
			"text",
			"textarea",
			"boolean",
			"select",
			"multiselect",
			"checkbox",
			"phone",
			"telephone",
			"email",
			"number",
			"decimal",
			"float",
			"integer",
			"date",
			"time",
			"datetime",
			"timestamp",
			"file",
			"upload",
			"attachment",
			"signature",
		];
		if (!supportedTypes.includes(data.type.toLowerCase())) {
			throw new Error(
				`CC Custom Field type "${data.type}" is not supported. Supported types: ${supportedTypes.join(", ")}`,
			);
		}

		try {
			const response = await ccRequest<{ customField: GetCCCustomField }>({
				url: "/customFields",
				method: "POST",
				data: {
					customField: cleanData(data),
				},
			});

			if (!response.customField) {
				throw new Error(
					"CC API returned invalid response: missing customField",
				);
			}

			return response.customField;
		} catch (error) {
			// Enhanced error handling with context
			if (error instanceof Error) {
				throw new Error(
					`Failed to create CC custom field "${data.name}": ${error.message}`,
				);
			}
			throw new Error(
				`Failed to create CC custom field "${data.name}": Unknown error`,
			);
		}
	},
};

/**
 * User operations
 */
export const ccUserReq = {
	/**
	 * Get a user by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCUserType> => {
		const response = await ccRequest<{ user: GetCCUserType }>({
			url: `/users/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.user;
	},

	/**
	 * Get all users
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCUserType[]> => {
		const response = await ccRequest<{ users: GetCCUserType[] }>({
			url: "/users",
			method: "GET",
			invalidateCache,
		});
		return response.users;
	},
};

/**
 * Service operations
 */
export const serviceReq = {
	/**
	 * Get all services
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCServiceType[]> => {
		const response = await ccRequest<{ services: GetCCServiceType[] }>({
			url: "/services",
			method: "GET",
			invalidateCache,
		});
		return response.services;
	},

	/**
	 * Get a service by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCServiceType> => {
		const response = await ccRequest<{ service: GetCCServiceType }>({
			url: `/services/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.service;
	},
};

/**
 * Resource operations
 */
export const resourceReq = {
	/**
	 * Get all resources
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCResourceType[]> => {
		const response = await ccRequest<{ resources: GetCCResourceType[] }>({
			url: "/resources",
			method: "GET",
			invalidateCache,
		});
		return response.resources;
	},

	/**
	 * Get a resource by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCResourceType> => {
		const response = await ccRequest<{ resource: GetCCResourceType }>({
			url: `/resources/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.resource;
	},
};

/**
 * Location operations
 */
export const ccLocationReq = {
	/**
	 * Get all locations
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCLocationType[]> => {
		const response = await ccRequest<{ locations: GetCCLocationType[] }>({
			url: "/locations",
			method: "GET",
			invalidateCache,
		});
		return response.locations;
	},

	/**
	 * Get a location by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCLocationType> => {
		const response = await ccRequest<{ location: GetCCLocationType }>({
			url: `/locations/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.location;
	},
};

/**
 * Appointment operations
 */
export const ccAppointmentReq = {
	/**
	 * Get an appointment by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: `/appointments/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.appointment;
	},

	/**
	 * Create a new appointment
	 */
	post: async (
		payload: PostCCAppointmentType,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: "/appointments",
			method: "POST",
			data: { appointment: payload },
		});
		return response.appointment;
	},

	/**
	 * Update an existing appointment
	 */
	put: async (
		id: number,
		payload: PutCCAppointmentType,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: `/appointments/${id}`,
			method: "PUT",
			data: { appointment: payload },
		});
		return response.appointment;
	},

	/**
	 * Appointment category operations
	 */
	category: {
		/**
		 * Get an appointment category by ID
		 */
		get: async (
			id: number,
			invalidateCache?: boolean,
		): Promise<GetCCAppointmentCategoryType> => {
			const response = await ccRequest<{
				appointmentCategory: GetCCAppointmentCategoryType;
			}>({
				url: `/appointmentCategories/${id}`,
				method: "GET",
				invalidateCache,
			});
			return response.appointmentCategory;
		},

		/**
		 * Get all appointment categories
		 */
		all: async (
			invalidateCache?: boolean,
		): Promise<GetCCAppointmentCategoryType[]> => {
			const response = await ccRequest<{
				appointmentCategories: GetCCAppointmentCategoryType[];
			}>({
				url: "/appointmentCategories",
				method: "GET",
				invalidateCache,
			});
			return response.appointmentCategories;
		},
	},
};

/**
 * Invoice operations
 */
export const invoiceReq = {
	/**
	 * Get invoices by IDs
	 */
	get: async (
		ids: number[],
		invalidateCache?: boolean,
	): Promise<GetInvoiceType[]> => {
		const response = await ccRequest<{ invoices: GetInvoiceType[] }>({
			url: `/invoices?${idsToQueryString(ids)}`,
			method: "GET",
			invalidateCache,
		});
		return response.invoices;
	},
};

/**
 * Payment operations
 */
export const paymentReq = {
	/**
	 * Get payments by IDs
	 */
	get: async (
		ids: number[],
		invalidateCache?: boolean,
	): Promise<GetPaymentType[]> => {
		const response = await ccRequest<{ payments: GetPaymentType[] }>({
			url: `/payments?${idsToQueryString(ids)}`,
			method: "GET",
			invalidateCache,
		});
		return response.payments;
	},
};
