# Phonebook Application

A modern phonebook application for Placetel & Yealink device contact synchronization with advanced user management and authentication.

## Features

- 🔐 **Authentication**: Email/password and Google OAuth with better-auth
- 📊 **Database**: PostgreSQL with Dr<PERSON>zle ORM and Neon hosting
- 📧 **Email**: Transactional emails with Resend
- 🎨 **UI**: Modern interface with Tailwind CSS and Radix UI
- 🚀 **Deployment**: Optimized for Cloudflare with OpenNext

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Authentication**: better-auth
- **Database**: PostgreSQL (Neon) with Drizzle ORM
- **Email**: Resend
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Deployment**: Cloudflare with OpenNext

## Quick Start

1. **Clone and install dependencies**:

```bash
gh repo clone AinCoderClient/DrLastPhonebook
cd DrLastPhonebook
pnpm install
```

2. **Set up environment variables**:

   All environment variables are configured in the `wrangler.jsonc` file under the "vars" section. Edit this file with your actual values:

```bash
nano wrangler.jsonc
# Update the "vars" section with your actual environment variable values
```

3. **Set up the database**:

```bash
pnpm db:push
```

4. **Start the development server**:

```bash
pnpm dev
```

5. **Open your browser**:

   Visit [http://localhost:3000](http://localhost:3000) to view the application.

## Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm db:push` - Push database schema changes
- `pnpm db:studio` - Open Drizzle Studio (if available)
- `pnpm lint` - Run ESLint

## Environment Variables

All environment variables are configured in the `wrangler.jsonc` file under the "vars" section. The following variables are required:

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `******************************` |
| `BETTER_AUTH_SECRET` | Secret for authentication | Random 32+ character string |
| `BETTER_AUTH_URL` | Base URL for auth callbacks | `http://localhost:3000` |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | From Google Console |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | From Google Console |
| `RESEND_API_KEY` | Resend API key for emails | From Resend dashboard |
| `EMAIL_FROM` | Default sender email | `<EMAIL>` |
| `EMAIL_REPLY_TO` | Reply-to email address | `<EMAIL>` |

## Deployment

This application is optimized for deployment on Cloudflare Workers using OpenNext:

```bash
pnpm build
wrangler deploy
```

## License

This project is private and proprietary.
