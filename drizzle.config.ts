import { defineConfig } from 'drizzle-kit';

// Note: This config uses the DATABASE_URL from wrangler.jsonc vars section
// The nodejs_compat_populate_process_env flag ensures process.env is populated in Workers runtime
export default defineConfig({
  schema: './lib/db/schema.ts',
  out: './lib/db/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",
  },
  verbose: true,
  strict: true,
});
