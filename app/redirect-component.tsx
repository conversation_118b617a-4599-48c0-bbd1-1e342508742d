"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

interface RedirectComponentProps {
  to: string
}

export function RedirectComponent({ to }: RedirectComponentProps) {
  const router = useRouter()

  useEffect(() => {
    router.replace(to)
  }, [router, to])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting...</p>
      </div>
    </div>
  )
}
