import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@/lib/auth/config';
import {
  createPhonebookKey,
  getUserPhonebookKeys,
  deletePhonebookKey,
  regeneratePhonebookSecret
} from '@/lib/phonebook-keys';

/**
 * GET /api/phonebook-keys
 * Get all phonebook keys for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const keys = await getUserPhonebookKeys(session.user.id);

    return NextResponse.json({
      keys: keys.map(key => ({
        id: key.id,
        key: key.key,
        secret: key.secret,
        createdAt: key.createdAt,
        updatedAt: key.updatedAt,
      })),
    });
  } catch (error) {
    console.error('Error fetching phonebook keys:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/phonebook-keys
 * Create a new phonebook key for the authenticated user
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const newKey = await createPhonebookKey(session.user.id);

    if (!newKey) {
      return NextResponse.json(
        { error: 'Failed to create phonebook key' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      key: {
        id: newKey.id,
        key: newKey.key,
        secret: newKey.secret,
        createdAt: newKey.createdAt,
        updatedAt: newKey.updatedAt,
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating phonebook key:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/phonebook-keys
 * Delete a phonebook key
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const keyId = searchParams.get('id');

    if (!keyId) {
      return NextResponse.json(
        { error: 'Missing key ID' },
        { status: 400 }
      );
    }

    const success = await deletePhonebookKey(keyId, session.user.id);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete phonebook key' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting phonebook key:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/phonebook-keys
 * Regenerate secret for a phonebook key
 */
export async function PUT(request: NextRequest) {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const keyId = searchParams.get('id');

    if (!keyId) {
      return NextResponse.json(
        { error: 'Missing key ID' },
        { status: 400 }
      );
    }

    const newSecret = await regeneratePhonebookSecret(keyId, session.user.id);

    if (!newSecret) {
      return NextResponse.json(
        { error: 'Failed to regenerate secret' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      secret: newSecret,
    });
  } catch (error) {
    console.error('Error regenerating phonebook secret:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
