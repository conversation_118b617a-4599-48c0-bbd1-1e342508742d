import { NextResponse } from 'next/server';
import { addServerLog } from '@/lib/logging';

/**
 * Test API to add sample logs for testing pagination
 * This should be removed in production
 */
export async function POST() {
  try {
    // Add 100 test logs to test pagination
    const logLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR'] as const;
    const sources = ['test-api', 'user-service', 'auth-service', 'db-service', 'cache-service'];
    const messages = [
      'User authentication successful',
      'Database connection established',
      'Cache invalidation triggered',
      'API request processed',
      'Background job completed',
      'Email notification sent',
      'File upload completed',
      'Data validation passed',
      'Session created',
      'Backup process started'
    ];

    for (let i = 0; i < 100; i++) {
      const level = logLevels[Math.floor(Math.random() * logLevels.length)];
      const source = sources[Math.floor(Math.random() * sources.length)];
      const message = messages[Math.floor(Math.random() * messages.length)] + ` #${i + 1}`;
      
      await addServerLog(level, message, source);
      
      // Small delay to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    return NextResponse.json({
      success: true,
      message: 'Added 100 test logs successfully',
    });
  } catch (error) {
    console.error('Error adding test logs:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to add test logs',
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST to add test logs',
    usage: 'POST /api/test-logs to add 100 sample logs for testing pagination',
  });
}
