import { getAuth } from "@/lib/auth/config";
import { headers } from "next/headers";
import { RedirectComponent } from "./redirect-component";

export default async function Home() {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    const redirectTo = session ? "/dashboard" : "/login";
    return <RedirectComponent to={redirectTo} />;
  } catch (error) {
    console.error("Auth check error:", error);
    return <RedirectComponent to="/login" />;
  }
}
