"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Shield, Clock, Trash2, Download, AlertTriangle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function LogManagement() {
  const [isCleaningUp, setIsCleaningUp] = useState(false)
  const [isAddingTestLogs, setIsAddingTestLogs] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleCleanupLogs = async () => {
    setIsCleaningUp(true)
    try {
      const response = await fetch('/api/gdpr/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json() as {
          success: boolean;
          details: {
            logsDeleted: number;
            logsAnonymized: number;
          };
        }
        toast({
          title: "Cleanup Successful",
          description: `Deleted ${result.details.logsDeleted} old logs and anonymized ${result.details.logsAnonymized} logs.`,
        })
        // Refresh server component data without full page reload
        router.refresh()
      } else {
        throw new Error('Cleanup failed')
      }
    } catch {
      toast({
        title: "Cleanup Failed",
        description: "Failed to cleanup old logs. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCleaningUp(false)
    }
  }

  const handleAddTestLogs = async () => {
    setIsAddingTestLogs(true)
    try {
      const response = await fetch('/api/test-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json() as { success: boolean; message: string }
        toast({
          title: "Test Logs Added",
          description: result.message,
        })
        // Refresh server component data without full page reload
        router.refresh()
      } else {
        throw new Error('Failed to add test logs')
      }
    } catch {
      toast({
        title: "Failed to Add Test Logs",
        description: "Could not add test logs. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsAddingTestLogs(false)
    }
  }

  const handleExportLogs = async () => {
    try {
      const response = await fetch('/api/gdpr/export')
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: "Export Successful",
          description: "Your data has been exported and downloaded.",
        })
      } else {
        throw new Error('Export failed')
      }
    } catch {
      toast({
        title: "Export Failed",
        description: "Failed to export your data. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-4">
      {/* GDPR Compliance Information */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertTitle>GDPR Compliance & Data Retention</AlertTitle>
        <AlertDescription className="mt-2 space-y-2">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>Server logs are automatically deleted after <strong>90 days</strong> for privacy compliance</span>
          </div>
          <div className="flex items-center gap-2">
            <Trash2 className="h-4 w-4" />
            <span>Logs older than 365 days are anonymized to remove personal data</span>
          </div>
          <div className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            <span>You can export your personal data at any time</span>
          </div>
        </AlertDescription>
      </Alert>

      {/* Log Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Log Management
          </CardTitle>
          <CardDescription>
            Manage server logs and data retention policies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <h4 className="font-medium mb-2">Add Test Logs</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Add 100 sample logs to test pagination and filtering functionality.
              </p>
              <Button
                onClick={handleAddTestLogs}
                disabled={isAddingTestLogs}
                variant="default"
                className="w-full sm:w-auto"
              >
                {isAddingTestLogs ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                    Adding logs...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Add Test Logs
                  </>
                )}
              </Button>
            </div>

            <div className="flex-1">
              <h4 className="font-medium mb-2">Cleanup Old Logs</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Manually trigger cleanup of logs older than 90 days and anonymization of logs older than 365 days.
              </p>
              <Button
                onClick={handleCleanupLogs}
                disabled={isCleaningUp}
                variant="outline"
                className="w-full sm:w-auto"
              >
                {isCleaningUp ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                    Cleaning up...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Cleanup Old Logs
                  </>
                )}
              </Button>
            </div>

            <div className="flex-1">
              <h4 className="font-medium mb-2">Export Your Data</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Download all your personal data including contacts, API keys, and recent logs.
              </p>
              <Button
                onClick={handleExportLogs}
                variant="outline"
                className="w-full sm:w-auto"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
            </div>
          </div>

          {/* Data Retention Policy */}
          <div className="border-t pt-4">
            <h4 className="font-medium mb-3">Data Retention Policy</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <Badge variant="default">90 Days</Badge>
                <span className="text-sm">Server logs retention</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">365 Days</Badge>
                <span className="text-sm">Log anonymization</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">2 Years</Badge>
                <span className="text-sm">Account inactivity threshold</span>
              </div>
            </div>
          </div>

          {/* Warning */}
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Important Notice</AlertTitle>
            <AlertDescription>
              Log cleanup is irreversible. Deleted logs cannot be recovered. 
              Export your data before cleanup if you need to retain specific information.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}
