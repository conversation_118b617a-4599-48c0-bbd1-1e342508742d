"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"

export function LogFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [level, setLevel] = useState<string>(searchParams.get('level') || 'all')
  const [source, setSource] = useState<string>(searchParams.get('source') || '')
  const [search, setSearch] = useState<string>(searchParams.get('search') || '')

  const updateFilters = () => {
    const params = new URLSearchParams(searchParams.toString())

    // Remove page when filtering to start from page 1
    params.delete('page')

    if (level && level !== 'all') {
      params.set('level', level)
    } else {
      params.delete('level')
    }

    if (source.trim()) {
      params.set('source', source.trim())
    } else {
      params.delete('source')
    }

    if (search.trim()) {
      params.set('search', search.trim())
    } else {
      params.delete('search')
    }

    const queryString = params.toString()
    const newUrl = queryString ? `/dashboard/logs?${queryString}` : '/dashboard/logs'
    router.replace(newUrl) // Use replace instead of push to avoid page reload
  }

  const clearFilters = () => {
    setLevel('all')
    setSource('')
    setSearch('')
    router.replace('/dashboard/logs') // Use replace to avoid page reload
  }

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center">
      <div className="flex flex-col gap-2 md:flex-row md:items-center md:gap-4 flex-1">
        <Select value={level} onValueChange={setLevel}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All levels</SelectItem>
            <SelectItem value="DEBUG">Debug</SelectItem>
            <SelectItem value="INFO">Info</SelectItem>
            <SelectItem value="WARN">Warning</SelectItem>
            <SelectItem value="ERROR">Error</SelectItem>
          </SelectContent>
        </Select>

        <Input
          placeholder="Search logs..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="w-[300px]"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              updateFilters()
            }
          }}
        />

        <Input
          placeholder="Filter by source..."
          value={source}
          onChange={(e) => setSource(e.target.value)}
          className="w-[200px]"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              updateFilters()
            }
          }}
        />

        <div className="flex gap-2">
          <Button onClick={updateFilters} size="sm">
            Apply Filters
          </Button>
          <Button onClick={clearFilters} variant="outline" size="sm">
            Clear
          </Button>
        </div>
      </div>
    </div>
  )
}
