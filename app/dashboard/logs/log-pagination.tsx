"use client"

import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"

interface LogPaginationProps {
  currentPage: number;
  totalPages: number;
}

export function LogPagination({ currentPage, totalPages }: LogPaginationProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const navigateToPage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString())

    if (page === 1) {
      params.delete('page')
    } else {
      params.set('page', page.toString())
    }

    const queryString = params.toString()
    const newUrl = queryString ? `/dashboard/logs?${queryString}` : '/dashboard/logs'
    router.replace(newUrl) // Use replace to avoid page reload and history pollution
  }

  return (
    <div className="flex items-center justify-center gap-2">
      {totalPages > 1 && (
        <>
          <Button
            variant="outline"
            disabled={currentPage <= 1}
            size="sm"
            onClick={() => navigateToPage(currentPage - 1)}
          >
            Previous
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            disabled={currentPage >= totalPages}
            size="sm"
            onClick={() => navigateToPage(currentPage + 1)}
          >
            Next
          </Button>
        </>
      )}
      {totalPages <= 1 && totalPages > 0 && (
        <span className="text-sm text-muted-foreground">
          Page 1 of 1
        </span>
      )}
    </div>
  )
}
