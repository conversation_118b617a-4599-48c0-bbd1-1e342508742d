// Check if we're in development mode
const isDevelopment = typeof window !== 'undefined'
  ? window.location.hostname === 'localhost'
  : process.env.NODE_ENV === 'development';

// Application configuration
export const config = {
  // App
  APP_URL: isDevelopment ? "http://localhost:3000" : "https://phonebook.aincoder.workers.dev",
  NEXTJS_ENV: isDevelopment ? "development" : "production",

  // Database
  DATABASE_URL: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",

  // Auth
  BETTER_AUTH_SECRET: "02af1eb8a1300afb62fb16ac5e9e2b4d9c90cf3a51197a37b9cd2125a0f8b772",
  BETTER_AUTH_URL: isDevelopment ? "http://localhost:3000" : "https://phonebook.aincoder.workers.dev",

  // OAuth
  GOOGLE_CLIENT_ID: "587567917765-qnd93vdv4oultgqgmmsm9t7ob1sduedt.apps.googleusercontent.com",
  GOOGLE_CLIENT_SECRET: "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",

  // Email
  EMAIL_FROM: "noreply@localhost",
  EMAIL_REPLY_TO: "support@localhost",
  RESEND_API_KEY: "re_DJ5zpVm6_AWrFbYWrWP7FicY5bKZ8yESy",

  CC_API_URL: "https://ccdemo.clinicore.eu/api/v1",
	CC_API_TOKEN:
		"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
} as const;

/**
 * Get configuration
 */
export function getConfig() {
  return config;
}

/**
 * Get client-side configuration (only public variables)
 */
export function getClientConfig() {
  return {
    APP_URL: config.APP_URL,
  };
}

/**
 * Validate required configuration
 */
export function validateConfig(): void {
  const cfg = getConfig();

  const required: (keyof typeof config)[] = [
    'DATABASE_URL',
    'BETTER_AUTH_SECRET',
    'BETTER_AUTH_URL',
  ];

  const missing = required.filter(key => !cfg[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(', ')}`);
  }
}
