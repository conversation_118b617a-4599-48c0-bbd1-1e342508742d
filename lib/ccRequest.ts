/**
 * Base HTTP client for ClinicalCare API with intelligent caching
 */
const ccRequest = async <
	T extends Record<string, unknown> = Record<string, unknown>,
>(
	options: RequestOptions,
): Promise<T> => {
	const { url, method, data, params, invalidateCache = false } = options;

	// Build full URL with base domain
	const baseUrl = getConfig("ccApiDomain");
	let fullUrl = `${baseUrl}${url}`;

	// Add query parameters if provided
	if (params) {
		const searchParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			searchParams.append(key, String(value));
		});
		fullUrl += `?${searchParams.toString()}`;
	}

	// Generate cache key for GET requests
	const cacheKey = apiResponseCache.generateKey(method, url, params);

	// Check cache for GET requests (unless cache invalidation is requested)
	if (method === "GET" && !invalidateCache) {
		const cachedData = apiResponseCache.get(cacheKey);
		if (cachedData) {
			return cachedData as T;
		}
	}

	// Prepare request headers
	const headers: Record<string, string> = {
		Authorization: `Bearer ${getConfig("ccApiKey")}`,
		Accept: "application/json",
		"Content-Type": "application/json",
	};

	try {
		const response = await fetch(fullUrl, {
			method,
			headers,
			body: data ? JSON.stringify(data) : undefined,
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.log("CC API Error errorText:", errorText);
			let errorMessage: string;
			let errorDetails: Record<string, unknown> = {};

			try {
				const errorData: ApiErrorResponse = JSON.parse(errorText);
				errorDetails = errorData;

				// Extract meaningful error message from various possible structures
				const firstError = errorData.errors?.[0];
				const firstErrorMessage =
					typeof firstError === "string" ? firstError : firstError?.message;

				errorMessage =
					errorData.message ||
					errorData.error ||
					firstErrorMessage ||
					errorData.detail ||
					errorData.title ||
					`HTTP ${response.status}: ${response.statusText}`;

				// If errorData has validation errors, include them
				if (errorData.errors && Array.isArray(errorData.errors)) {
					const validationErrors = errorData.errors
						.map((err: string | ValidationError) =>
							typeof err === "string"
								? err
								: err.message || err.detail || JSON.stringify(err),
						)
						.join("; ");
					errorMessage += ` | Validation errors: ${validationErrors}`;
				}
			} catch {
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
				errorDetails = { rawResponse: errorText };
			}

			// Enhanced error with detailed information
			const enhancedError = new Error(
				`CC API Error: ${errorMessage}`,
			) as EnhancedError;
			enhancedError.details = {
				status: response.status,
				statusText: response.statusText,
				url: fullUrl,
				method,
				requestData: data,
				responseData: errorDetails,
				timestamp: new Date().toISOString(),
			};

			throw enhancedError;
		}

		const responseData = await response.json();

		// Cache successful GET responses
		if (method === "GET") {
			apiResponseCache.set(cacheKey, responseData);
		}

		// Invalidate related cache entries for mutating operations
		if (method === "POST" || method === "PUT" || method === "DELETE") {
			const invalidatedCount = apiResponseCache.invalidatePattern(url);
			if (invalidatedCount > 0) {
				logDebug(
					`CC API: Invalidated ${invalidatedCount} cache entries for ${method} ${url}`,
				);
			}
		}

		return responseData as T;
	} catch (error) {
		if (error instanceof Error) {
			// If this is our enhanced error with details, preserve them
			const enhancedError = error as EnhancedError;
			if (enhancedError.details) {
				const newEnhancedError = new Error(
					`CC API Request Failed: ${error.message}`,
				) as EnhancedError;
				newEnhancedError.details = enhancedError.details;
				throw newEnhancedError;
			}
			throw new Error(`CC API Request Failed: ${error.message}`);
		}
		throw new Error("CC API Request Failed: Unknown error");
	}
};