import { getConfig } from '@/lib/config';

// Simple types
type RequestOptions = {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: Record<string, unknown>;
  params?: Record<string, string | number>;
};

/**
 * Simple HTTP client for ClinicalCare API
 */
const ccRequest = async <T = Record<string, unknown>>(
  options: RequestOptions,
): Promise<T> => {
  const { url, method, data, params } = options;
  const config = getConfig();

  // Build URL
  let fullUrl = `${config.CC_API_URL}${url}`;
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, String(value));
    });
    fullUrl += `?${searchParams.toString()}`;
  }

  // Make request
  const response = await fetch(fullUrl, {
    method,
    headers: {
      Authorization: `Bearer ${config.CC_API_TOKEN}`,
      'Content-Type': 'application/json',
    },
    body: data ? JSON.stringify(data) : undefined,
  });

  if (!response.ok) {
    throw new Error(`CC API Error: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

export default ccRequest;