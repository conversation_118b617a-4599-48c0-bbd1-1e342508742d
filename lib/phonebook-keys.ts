import { getDb, phonebookKeyPasswords } from '@/lib/db';
import { eq, and } from 'drizzle-orm';
import { addServerLog } from '@/lib/logging';

export interface PhonebookKey {
  id: string;
  userId: string;
  key: string;
  secret: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Generate a secure random string for API keys and secrets
 */
function generateSecureString(length: number = 32): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.~';
  const charsetLength = charset.length;

  let result = '';

  // Use global crypto if available (browser/edge runtime) or require crypto module (Node.js)
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const randomValues = new Uint8Array(length);
    crypto.getRandomValues(randomValues);

    for (let i = 0; i < length; i++) {
      const index = randomValues[i] % charsetLength;
      result += charset.charAt(index);
    }
  } else {
    // Fallback for environments without crypto.getRandomValues
    for (let i = 0; i < length; i++) {
      const index = Math.floor(Math.random() * charsetLength);
      result += charset.charAt(index);
    }
  }

  return result;
}

/**
 * Create a new phonebook key/secret pair for a user
 */
export async function createPhonebookKey(userId: string): Promise<PhonebookKey | null> {
  try {
    const db = await getDb();
    
    // Generate secure key and secret
    const key = generateSecureString(24);
    const secret = generateSecureString(48);
    
    const result = await db
      .insert(phonebookKeyPasswords)
      .values({
        userId,
        key,
        secret,
      })
      .returning();
    
    if (result.length === 0) {
      throw new Error('Failed to create phonebook key');
    }
    
    const newKey = result[0];
    
    await addServerLog(
      'INFO',
      `Created new phonebook API key for user`,
      'phonebook-keys',
      userId
    );
    
    return {
      id: newKey.id,
      userId: newKey.userId,
      key: newKey.key,
      secret: newKey.secret,
      createdAt: newKey.createdAt,
      updatedAt: newKey.updatedAt,
    };
  } catch (error) {
    console.error('Error creating phonebook key:', error);
    await addServerLog(
      'ERROR',
      `Failed to create phonebook API key: ${error}`,
      'phonebook-keys',
      userId
    );
    return null;
  }
}

/**
 * Get all phonebook keys for a user
 */
export async function getUserPhonebookKeys(userId: string): Promise<PhonebookKey[]> {
  try {
    const db = await getDb();
    
    const keys = await db
      .select()
      .from(phonebookKeyPasswords)
      .where(eq(phonebookKeyPasswords.userId, userId))
      .orderBy(phonebookKeyPasswords.createdAt);
    
    return keys.map(key => ({
      id: key.id,
      userId: key.userId,
      key: key.key,
      secret: key.secret,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
    }));
  } catch (error) {
    console.error('Error fetching phonebook keys:', error);
    return [];
  }
}

/**
 * Delete a phonebook key
 */
export async function deletePhonebookKey(keyId: string, userId: string): Promise<boolean> {
  try {
    const db = await getDb();
    
    const result = await db
      .delete(phonebookKeyPasswords)
      .where(
        and(
          eq(phonebookKeyPasswords.id, keyId),
          eq(phonebookKeyPasswords.userId, userId)
        )
      );
    
    if (result.rowCount && result.rowCount > 0) {
      await addServerLog(
        'INFO',
        `Deleted phonebook API key: ${keyId}`,
        'phonebook-keys',
        userId
      );
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error deleting phonebook key:', error);
    await addServerLog(
      'ERROR',
      `Failed to delete phonebook API key: ${error}`,
      'phonebook-keys',
      userId
    );
    return false;
  }
}

/**
 * Validate phonebook key and secret
 */
export async function validatePhonebookCredentials(
  key: string,
  secret: string
): Promise<{ userId: string; keyId: string } | null> {
  try {
    const db = await getDb();
    
    const result = await db
      .select({
        id: phonebookKeyPasswords.id,
        userId: phonebookKeyPasswords.userId,
      })
      .from(phonebookKeyPasswords)
      .where(
        and(
          eq(phonebookKeyPasswords.key, key),
          eq(phonebookKeyPasswords.secret, secret)
        )
      )
      .limit(1);
    
    if (result.length === 0) {
      return null;
    }
    
    return {
      userId: result[0].userId,
      keyId: result[0].id,
    };
  } catch (error) {
    console.error('Error validating phonebook credentials:', error);
    return null;
  }
}

/**
 * Regenerate secret for an existing key
 */
export async function regeneratePhonebookSecret(keyId: string, userId: string): Promise<string | null> {
  try {
    const db = await getDb();
    
    const newSecret = generateSecureString(48);
    
    const result = await db
      .update(phonebookKeyPasswords)
      .set({
        secret: newSecret,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(phonebookKeyPasswords.id, keyId),
          eq(phonebookKeyPasswords.userId, userId)
        )
      )
      .returning();
    
    if (result.length === 0) {
      return null;
    }
    
    await addServerLog(
      'INFO',
      `Regenerated secret for phonebook API key: ${keyId}`,
      'phonebook-keys',
      userId
    );
    
    return newSecret;
  } catch (error) {
    console.error('Error regenerating phonebook secret:', error);
    await addServerLog(
      'ERROR',
      `Failed to regenerate phonebook secret: ${error}`,
      'phonebook-keys',
      userId
    );
    return null;
  }
}

/**
 * Input sanitization utilities for API security
 */
export const sanitization = {
  /**
   * Sanitize string input to prevent injection attacks
   */
  sanitizeString(input: string, maxLength: number = 255): string {
    if (!input) return '';
    
    return input
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/[<>'"&]/g, '') // Remove potentially dangerous characters
      .trim()
      .substring(0, maxLength);
  },
  
  /**
   * Sanitize phone number input
   */
  sanitizePhoneNumber(input: string): string {
    if (!input) return '';
    
    return input
      .replace(/[^0-9+\-\s\(\)\.]/g, '') // Only allow phone number characters
      .trim()
      .substring(0, 50);
  },
  
  /**
   * Sanitize email input
   */
  sanitizeEmail(input: string): string {
    if (!input) return '';
    
    return input
      .replace(/[^a-zA-Z0-9@\.\-_]/g, '') // Only allow email characters
      .toLowerCase()
      .trim()
      .substring(0, 255);
  },
  
  /**
   * Validate and sanitize API key format
   */
  sanitizeApiKey(input: string): string {
    if (!input) return '';
    
    return input
      .replace(/[^a-zA-Z0-9\-_]/g, '') // Only allow alphanumeric, dash, underscore
      .trim()
      .substring(0, 255);
  },
  
  /**
   * Validate and sanitize API secret format
   */
  sanitizeApiSecret(input: string): string {
    if (!input) return '';
    
    return input
      .replace(/[^a-zA-Z0-9\-_.~]/g, '') // Allow URL-safe characters
      .trim()
      .substring(0, 255);
  },
};

/**
 * Rate limiting utilities (basic implementation)
 */
export const rateLimiting = {
  // Simple in-memory rate limiting (for production, use Redis or similar)
  attempts: new Map<string, { count: number; resetTime: number }>(),
  
  /**
   * Check if IP/key is rate limited
   */
  isRateLimited(identifier: string, maxAttempts: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);
    
    if (!record || now > record.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + windowMs });
      return false;
    }
    
    if (record.count >= maxAttempts) {
      return true;
    }
    
    record.count++;
    return false;
  },
  
  /**
   * Clear rate limit for identifier
   */
  clearRateLimit(identifier: string): void {
    this.attempts.delete(identifier);
  },
};
