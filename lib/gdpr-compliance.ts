import { getDb, serverLogs, contacts, phonebookKeyPasswords, user } from '@/lib/db';
import { eq, lte, and, gte } from 'drizzle-orm';
import { addServerLog } from '@/lib/logging';

/**
 * GDPR Compliance Module
 * 
 * This module implements GDPR compliance features including:
 * - Data retention policies
 * - Data anonymization
 * - User data export
 * - User data deletion (Right to be forgotten)
 * - Privacy-compliant logging
 */

export interface DataRetentionPolicy {
  serverLogs: number; // days
  userSessions: number; // days
  apiAccessLogs: number; // days
  userInactivity: number; // days before account considered inactive
}

// Default GDPR-compliant data retention policy
export const DEFAULT_RETENTION_POLICY: DataRetentionPolicy = {
  serverLogs: 90, // 3 months for security logs
  userSessions: 30, // 1 month for session data
  apiAccessLogs: 180, // 6 months for API access logs
  userInactivity: 730, // 2 years before account considered inactive
};

/**
 * Clean up old server logs according to retention policy
 */
export async function cleanupServerLogs(retentionDays: number = DEFAULT_RETENTION_POLICY.serverLogs): Promise<number> {
  try {
    const db = await getDb();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await db
      .delete(serverLogs)
      .where(lte(serverLogs.createdAt, cutoffDate));

    const deletedCount = result.rowCount || 0;

    await addServerLog(
      'INFO',
      `GDPR cleanup: Deleted ${deletedCount} server logs older than ${retentionDays} days`,
      'gdpr-cleanup'
    );

    return deletedCount;
  } catch (error) {
    console.error('Error cleaning up server logs:', error);
    await addServerLog(
      'ERROR',
      `GDPR cleanup failed for server logs: ${error}`,
      'gdpr-cleanup'
    );
    return 0;
  }
}

/**
 * Anonymize old logs by removing personal identifiers
 */
export async function anonymizeOldLogs(anonymizationDays: number = 365): Promise<number> {
  try {
    const db = await getDb();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - anonymizationDays);

    // Anonymize logs by removing user IDs and phonebook key references
    const result = await db
      .update(serverLogs)
      .set({
        userId: null,
        phonebookKeyPasswordsId: null,
        message: 'ANONYMIZED: Personal data removed for GDPR compliance',
        updatedAt: new Date(),
      })
      .where(
        and(
          lte(serverLogs.createdAt, cutoffDate),
          // Only anonymize logs that still have personal data
          eq(serverLogs.userId, serverLogs.userId) // This will match non-null userIds
        )
      );

    const anonymizedCount = result.rowCount || 0;

    await addServerLog(
      'INFO',
      `GDPR anonymization: Anonymized ${anonymizedCount} server logs older than ${anonymizationDays} days`,
      'gdpr-anonymization'
    );

    return anonymizedCount;
  } catch (error) {
    console.error('Error anonymizing old logs:', error);
    await addServerLog(
      'ERROR',
      `GDPR anonymization failed: ${error}`,
      'gdpr-anonymization'
    );
    return 0;
  }
}

interface UserProfile {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image: string | null;
  createdAt: Date;
  updatedAt: Date;
  role: "user" | "admin" | null;
}

interface ContactData {
  id: string;
  firstName: string;
  lastName: string | null;
  email: string | null;
  phone: string | null;
  company: string | null;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface ApiKeyData {
  id: string;
  key: string;
  createdAt: Date;
  updatedAt: Date;
}

interface LogData {
  timestamp: Date;
  level: string;
  message: string;
  source: string;
  createdAt: Date;
}

export interface UserDataExport {
  exportDate: string;
  user: UserProfile | null;
  contacts: ContactData[];
  apiKeys: ApiKeyData[];
  recentLogs: LogData[];
  dataRetentionPolicy: DataRetentionPolicy;
  gdprNotice: {
    purpose: string;
    retention: string;
    rights: string;
    contact: string;
  };
}

/**
 * Export all user data for GDPR data portability
 */
export async function exportUserData(userId: string): Promise<UserDataExport> {
  try {
    const db = await getDb();

    // Get user profile
    const userProfile = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    // Get user contacts
    const userContacts = await db
      .select()
      .from(contacts)
      .where(eq(contacts.userId, userId));

    // Get user phonebook keys (without secrets for security)
    const userKeys = await db
      .select({
        id: phonebookKeyPasswords.id,
        key: phonebookKeyPasswords.key,
        createdAt: phonebookKeyPasswords.createdAt,
        updatedAt: phonebookKeyPasswords.updatedAt,
      })
      .from(phonebookKeyPasswords)
      .where(eq(phonebookKeyPasswords.userId, userId));

    // Get user's recent logs (last 90 days only for privacy)
    const recentDate = new Date();
    recentDate.setDate(recentDate.getDate() - 90);
    
    const userLogs = await db
      .select({
        timestamp: serverLogs.timestamp,
        level: serverLogs.level,
        message: serverLogs.message,
        source: serverLogs.source,
        createdAt: serverLogs.createdAt,
      })
      .from(serverLogs)
      .where(
        and(
          eq(serverLogs.userId, userId),
          gte(serverLogs.createdAt, recentDate)
        )
      )
      .orderBy(serverLogs.timestamp);

    const exportData = {
      exportDate: new Date().toISOString(),
      user: userProfile[0] || null,
      contacts: userContacts,
      apiKeys: userKeys,
      recentLogs: userLogs,
      dataRetentionPolicy: DEFAULT_RETENTION_POLICY,
      gdprNotice: {
        purpose: 'This data export contains all personal data we have stored about you.',
        retention: 'Data is retained according to our GDPR-compliant retention policy.',
        rights: 'You have the right to rectification, erasure, and data portability.',
        contact: 'For questions about your data, please contact our data protection officer.',
      },
    };

    await addServerLog(
      'INFO',
      'User data export generated',
      'gdpr-export',
      userId
    );

    return exportData;
  } catch (error) {
    console.error('Error exporting user data:', error);
    await addServerLog(
      'ERROR',
      `User data export failed: ${error}`,
      'gdpr-export',
      userId
    );
    throw error;
  }
}

/**
 * Delete all user data (Right to be forgotten)
 */
export async function deleteUserData(userId: string, keepLogs: boolean = true): Promise<boolean> {
  try {
    const db = await getDb();

    // Start transaction
    await db.transaction(async (tx) => {
      // Delete user contacts
      await tx.delete(contacts).where(eq(contacts.userId, userId));

      // Delete phonebook keys
      await tx.delete(phonebookKeyPasswords).where(eq(phonebookKeyPasswords.userId, userId));

      if (!keepLogs) {
        // Delete user logs completely
        await tx.delete(serverLogs).where(eq(serverLogs.userId, userId));
      } else {
        // Anonymize user logs (recommended for audit trail)
        await tx
          .update(serverLogs)
          .set({
            userId: null,
            message: 'DELETED: User data removed per GDPR request',
            updatedAt: new Date(),
          })
          .where(eq(serverLogs.userId, userId));
      }

      // Note: We don't delete the user account itself here as that's handled by the auth system
      // The user table should be handled separately to maintain referential integrity
    });

    await addServerLog(
      'INFO',
      `User data deletion completed (keepLogs: ${keepLogs})`,
      'gdpr-deletion',
      keepLogs ? userId : undefined // Don't log userId if we're deleting everything
    );

    return true;
  } catch (error) {
    console.error('Error deleting user data:', error);
    await addServerLog(
      'ERROR',
      `User data deletion failed: ${error}`,
      'gdpr-deletion',
      userId
    );
    return false;
  }
}

/**
 * Run automated GDPR compliance cleanup
 */
export async function runGdprCompliance(policy: DataRetentionPolicy = DEFAULT_RETENTION_POLICY): Promise<{
  logsDeleted: number;
  logsAnonymized: number;
  success: boolean;
}> {
  try {
    await addServerLog(
      'INFO',
      'Starting automated GDPR compliance cleanup',
      'gdpr-automation'
    );

    // Clean up old logs
    const logsDeleted = await cleanupServerLogs(policy.serverLogs);

    // Anonymize older logs (keep for audit but remove personal data)
    const logsAnonymized = await anonymizeOldLogs(policy.apiAccessLogs);

    await addServerLog(
      'INFO',
      `GDPR compliance completed: ${logsDeleted} logs deleted, ${logsAnonymized} logs anonymized`,
      'gdpr-automation'
    );

    return {
      logsDeleted,
      logsAnonymized,
      success: true,
    };
  } catch (error) {
    console.error('GDPR compliance automation failed:', error);
    await addServerLog(
      'ERROR',
      `GDPR compliance automation failed: ${error}`,
      'gdpr-automation'
    );

    return {
      logsDeleted: 0,
      logsAnonymized: 0,
      success: false,
    };
  }
}

/**
 * Privacy-compliant logging utilities
 */
export const privacyLogging = {
  /**
   * Mask sensitive data in log messages
   */
  maskSensitiveData(message: string): string {
    return message
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]') // Mask emails
      .replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE]') // Mask phone numbers
      .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP]') // Mask IP addresses
      .replace(/password[=:]\s*\S+/gi, 'password=[REDACTED]') // Mask passwords
      .replace(/secret[=:]\s*\S+/gi, 'secret=[REDACTED]') // Mask secrets
      .replace(/token[=:]\s*\S+/gi, 'token=[REDACTED]'); // Mask tokens
  },

  /**
   * Create GDPR-compliant log entry
   */
  createCompliantLogMessage(action: string, details?: string): string {
    const baseMessage = `GDPR-compliant action: ${action}`;
    if (details) {
      return `${baseMessage} - ${this.maskSensitiveData(details)}`;
    }
    return baseMessage;
  },
};
