import { getResend, getEmailConfig } from './config';

export interface EmailVerificationData {
  email: string;
  name?: string;
  verificationUrl: string;
}

export interface PasswordResetData {
  email: string;
  name?: string;
  resetUrl: string;
}

export interface WelcomeEmailData {
  email: string;
  name?: string;
}

export const sendVerificationEmail = async (data: EmailVerificationData) => {
  try {
    const resend = await getResend();
    const emailConfig = await getEmailConfig();

    const { data: result, error } = await resend.emails.send({
      from: emailConfig.from,
      to: data.email,
      subject: 'Verify your email address',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333; text-align: center;">Verify Your Email Address</h1>
          <p>Hello ${data.name || 'there'},</p>
          <p>Thank you for signing up! Please click the button below to verify your email address:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.verificationUrl}" 
               style="background-color: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${data.verificationUrl}</p>
          <p>This link will expire in 24 hours for security reasons.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            If you didn't create an account, you can safely ignore this email.
          </p>
        </div>
      `,
    });

    if (error) {
      console.error('Failed to send verification email:', error);
      throw new Error('Failed to send verification email');
    }

    return result;
  } catch (error) {
    console.error('Email service error:', error);
    throw error;
  }
};

export const sendPasswordResetEmail = async (data: PasswordResetData) => {
  try {
    const resend = await getResend();
    const emailConfig = await getEmailConfig();

    const { data: result, error } = await resend.emails.send({
      from: emailConfig.from,
      to: data.email,
      subject: 'Reset your password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333; text-align: center;">Reset Your Password</h1>
          <p>Hello ${data.name || 'there'},</p>
          <p>We received a request to reset your password. Click the button below to create a new password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.resetUrl}" 
               style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${data.resetUrl}</p>
          <p>This link will expire in 1 hour for security reasons.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            If you didn't request a password reset, you can safely ignore this email.
          </p>
        </div>
      `,
    });

    if (error) {
      console.error('Failed to send password reset email:', error);
      throw new Error('Failed to send password reset email');
    }

    return result;
  } catch (error) {
    console.error('Email service error:', error);
    throw error;
  }
};

export const sendWelcomeEmail = async (data: WelcomeEmailData) => {
  try {
    const resend = await getResend();
    const emailConfig = await getEmailConfig();

    const { data: result, error } = await resend.emails.send({
      from: emailConfig.from,
      to: data.email,
      subject: 'Welcome to Phonebook!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333; text-align: center;">Welcome to Phonebook!</h1>
          <p>Hello ${data.name || 'there'},</p>
          <p>Welcome to your new phonebook application! We're excited to have you on board.</p>
          <p>You can now:</p>
          <ul>
            <li>Add and manage your contacts</li>
            <li>Search through your phonebook</li>
            <li>Keep your contact information organized</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard" 
               style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Get Started
            </a>
          </div>
          <p>If you have any questions, feel free to reach out to our support team.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            Thank you for choosing Phonebook!
          </p>
        </div>
      `,
    });

    if (error) {
      console.error('Failed to send welcome email:', error);
      throw new Error('Failed to send welcome email');
    }

    return result;
  } catch (error) {
    console.error('Email service error:', error);
    throw error;
  }
};
