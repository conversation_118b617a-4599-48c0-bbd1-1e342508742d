import { Resend } from 'resend';
import { getConfig } from '@/lib/config';

// Resend instance cache
let resendInstance: Resend | null = null;

/**
 * Get Resend instance
 */
export function getResend(): Resend {
  if (resendInstance) {
    return resendInstance;
  }

  const config = getConfig();

  if (!config.RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY is required');
  }

  resendInstance = new Resend(config.RESEND_API_KEY);
  return resendInstance;
}

/**
 * Get email configuration
 */
export function getEmailConfig(): { from: string; replyTo: string } {
  const config = getConfig();

  return {
    from: config.EMAIL_FROM,
    replyTo: config.EMAIL_REPLY_TO,
  };
}
