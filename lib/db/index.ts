import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import * as schema from './schema';
import { getConfig } from '@/lib/config';

// Database instance and pool cache for performance
let dbInstance: ReturnType<typeof drizzle> | null = null;
let poolInstance: Pool | null = null;

/**
 * Get database instance with optimized connection pooling
 * Implements connection reuse and proper error handling
 */
export async function getDb() {
  if (dbInstance && poolInstance) {
    return dbInstance;
  }

  const config = getConfig();

  if (!config.DATABASE_URL) {
    throw new Error('DATABASE_URL is required for database connection');
  }

  try {
    // Create a connection pool with optimized settings
    if (!poolInstance) {
      poolInstance = new Pool({
        connectionString: config.DATABASE_URL,
        // Optimize for serverless environments
        max: 10, // Maximum number of connections
        idleTimeoutMillis: 30000, // 30 seconds
        connectionTimeoutMillis: 5000, // 5 seconds
      });
    }

    // Create the database instance with schema
    if (!dbInstance) {
      dbInstance = drizzle(poolInstance, {
        schema,
        logger: process.env.NODE_ENV === 'development' ? true : false
      });
    }

    return dbInstance;
  } catch (error) {
    console.error('Failed to initialize database connection:', error);
    throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Close database connections (useful for cleanup in tests or shutdown)
 */
export async function closeDb(): Promise<void> {
  try {
    if (poolInstance) {
      await poolInstance.end();
      poolInstance = null;
    }
    dbInstance = null;
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
}

// Note: For CLI tools and build-time operations, use getDb() function
// All environment variables are configured in wrangler.jsonc under the "vars" section

// Export schema for use in other files
export * from './schema';
