import {
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
  boolean,
  index,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

// Better Auth user table
export const user = pgTable("user", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified")
    .$defaultFn(() => false)
    .notNull(),
  image: text("image"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
  role: varchar("role", { length: 255, enum: ["admin", "user"] }).default("user"),
});

// Better Auth account table
export const account = pgTable("account", {
  id: text("id").primaryKey(),
  accountId: text("account_id").notNull(),
  providerId: text("provider_id").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  accessTokenExpiresAt: timestamp("access_token_expires_at"),
  refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
});

// Better Auth session table
export const session = pgTable("session", {
  id: text("id").primaryKey(),
  expiresAt: timestamp("expires_at").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
});

// Better Auth verification table
export const verification = pgTable("verification", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date()),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date()),
});

// Better Auth schema export
export const authSchema = {
  user,
  account,
  session,
  verification,
};

// Contacts table for the phonebook
export const contacts = pgTable(
  "contacts",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: text("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    firstName: varchar("first_name", { length: 255 }).notNull(),
    lastName: varchar("last_name", { length: 255 }),
    email: varchar("email", { length: 255 }),
    phone: varchar("phone", { length: 50 }),
    company: varchar("company", { length: 255 }),
    notes: text("notes"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdIdx: index("contacts_user_id_idx").on(table.userId),
    nameIdx: index("contacts_name_idx").on(table.firstName, table.lastName),
    emailIdx: index("contacts_email_idx").on(table.email),
  })
);

export const patients = pgTable("patients", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  phone: varchar("phone", { length: 50 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const phonebookKeyPasswords = pgTable("phonebook_key_passwords", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  key: varchar("key", { length: 255 })
    .notNull()
    .$defaultFn(() => crypto.randomUUID()),
  secret: varchar("password", { length: 255 })
    .notNull()
    .$defaultFn(() => {
      const charset =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.~";
      const charsetLength = charset.length;
      const secretLength = 48;

      let password = "";
      const randomValues = new Uint8Array(secretLength);
      crypto.getRandomValues(randomValues);

      for (let i = 0; i < secretLength; i++) {
        const index = randomValues[i] % charsetLength;
        password += charset.charAt(index);
      }
      return password;
    }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const serverLogs = pgTable("server_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: text("user_id").references(() => user.id, { onDelete: "cascade" }),
  phonebookKeyPasswordsId: uuid("phonebook_key_passwords_id").references(
    () => phonebookKeyPasswords.id,
    { onDelete: "cascade" }
  ),
  timestamp: timestamp("timestamp").notNull(),
  level: varchar("level", { length: 255 }).notNull(),
  message: text("message").notNull(),
  source: varchar("source", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Relations
export const userRelations = relations(user, ({ many }) => ({
  account: many(account),
  session: many(session),
  contacts: many(contacts),
  phonebookKeys: many(phonebookKeyPasswords),
  logs: many(serverLogs),
}));

export const accountRelations = relations(account, ({ one }) => ({
  user: one(user, {
    fields: [account.userId],
    references: [user.id],
  }),
}));

export const sessionRelations = relations(session, ({ one }) => ({
  user: one(user, {
    fields: [session.userId],
    references: [user.id],
  }),
}));

export const contactsRelations = relations(contacts, ({ one }) => ({
  user: one(user, {
    fields: [contacts.userId],
    references: [user.id],
  }),
}));

export const phonebookKeyPasswordsRelations = relations(
  phonebookKeyPasswords,
  ({ one }) => ({
    user: one(user, {
      fields: [phonebookKeyPasswords.userId],
      references: [user.id],
    }),
  })
);

export const serverLogsRelations = relations(serverLogs, ({ one }) => ({
  user: one(user, {
    fields: [serverLogs.userId],
    references: [user.id],
  }),
  phonebookKeyPasswords: one(phonebookKeyPasswords, {
    fields: [serverLogs.phonebookKeyPasswordsId],
    references: [phonebookKeyPasswords.id],
  }),
}));

// Export types
export type TUser = typeof user.$inferSelect;
export type TNewUser = typeof user.$inferInsert;
export type TAccount = typeof account.$inferSelect;
export type TNewAccount = typeof account.$inferInsert;
export type TSession = typeof session.$inferSelect;
export type TNewSession = typeof session.$inferInsert;
export type TVerification = typeof verification.$inferSelect;
export type TNewVerification = typeof verification.$inferInsert;
export type TContact = typeof contacts.$inferSelect;
export type TNewContact = typeof contacts.$inferInsert;
export type TPatient = typeof patients.$inferSelect;
export type TNewPatient = typeof patients.$inferInsert;
export type TServerLog = typeof serverLogs.$inferSelect;
export type TNewServerLog = typeof serverLogs.$inferInsert;
export type TPhonebookKeyPassword = typeof phonebookKeyPasswords.$inferSelect;
export type TNewPhonebookKeyPassword = typeof phonebookKeyPasswords.$inferInsert;
