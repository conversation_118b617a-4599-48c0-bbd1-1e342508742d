import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@/lib/auth/config';
import { logHttpRequest } from '@/lib/logging';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const startTime = Date.now();

  // Skip middleware for static files but log API routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon.ico')
  ) {
    return NextResponse.next();
  }

  // Non-blocking logging function
  const logRequest = (response?: NextResponse, userId?: string): void => {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'Unknown';

    logHttpRequest(
      request.method,
      pathname,
      userAgent,
      ipAddress,
      userId,
      response?.status,
      responseTime
    );
  };

  // Skip auth middleware for API routes but still log them
  if (pathname.startsWith('/api/auth')) {
    const response = NextResponse.next();
    logRequest(response);
    return response;
  }

  // Get session for authentication check
  let session: { user?: { id: string } } | null = null;
  let userId: string | undefined;
  try {
    const auth = await getAuth();
    session = await auth.api.getSession({
      headers: request.headers,
    });
    userId = session?.user?.id;
  } catch (error) {
    console.error('Auth middleware error:', error);
  }

  // Redirect authenticated users away from auth pages
  if (session && (
    pathname.startsWith('/login') ||
    pathname.startsWith('/register') ||
    pathname.startsWith('/forgot-password') ||
    pathname.startsWith('/reset-password')
  )) {
    const response = NextResponse.redirect(new URL('/dashboard', request.url));
    logRequest(response, userId);
    return response;
  }

  // Allow access to root page (it handles its own redirect)
  if (pathname === '/') {
    const response = NextResponse.next();
    logRequest(response, userId);
    return response;
  }

  // Check if user is authenticated for protected routes
  if (pathname.startsWith('/dashboard')) {
    if (!session) {
      const response = NextResponse.redirect(new URL('/login', request.url));
      logRequest(response, userId);
      return response;
    }
  }

  const response = NextResponse.next();
  logRequest(response, userId);
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Note: We now include API routes for logging purposes
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
